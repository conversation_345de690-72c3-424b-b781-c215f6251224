class MigrateLegacyFilesToNewSystem < ActiveRecord::Migration[7.1]
  def up
    # This migration queues background jobs to migrate legacy file records
    # to the new Files::File and Files::FileAttachment system.
    #
    # The migration is designed to be safe and idempotent:
    # - Each container's files are migrated atomically in separate jobs
    # - Jobs can be retried if they fail
    # - Already migrated containers are skipped

    say "Queuing legacy file migration jobs..."

    # Define the legacy file mappings (same as in the service)
    legacy_file_mappings = {
      'ProjectFile' => {
        container_class: 'Project',
        container_foreign_key: 'project_id',
        legacy_relationship: 'project_files'
      },
      'IdeaFile' => {
        container_class: 'Idea',
        container_foreign_key: 'idea_id',
        legacy_relationship: 'idea_files'
      },
      'EventFile' => {
        container_class: 'Event',
        container_foreign_key: 'event_id',
        legacy_relationship: 'event_files'
      },
      'PhaseFile' => {
        container_class: 'Phase',
        container_foreign_key: 'phase_id',
        legacy_relationship: 'phase_files'
      },
      'StaticPageFile' => {
        container_class: 'StaticPage',
        container_foreign_key: 'static_page_id',
        legacy_relationship: 'static_page_files'
      },
      'ProjectFolders::File' => {
        container_class: 'ProjectFolders::Folder',
        container_foreign_key: 'project_folder_id',
        legacy_relationship: 'files'
      }
    }

    total_jobs_queued = 0

    legacy_file_mappings.each do |legacy_model_name, config|
      container_class = config[:container_class].constantize
      legacy_relationship = config[:legacy_relationship]

      # Find all containers that have legacy files
      containers_with_legacy_files = container_class
        .joins(legacy_relationship.to_sym)
        .distinct
        .select(:id)

      containers_count = containers_with_legacy_files.count
      say "Found #{containers_count} #{config[:container_class]} containers with legacy files"

      # Queue a job for each container
      containers_with_legacy_files.find_each do |container|
        Files::LegacyFileMigrationJob.perform_later(
          container_type: config[:container_class],
          container_id: container.id
        )
        total_jobs_queued += 1
      end
    end

    say "Queued #{total_jobs_queued} migration jobs"
    say "Migration jobs will run in the background. Monitor job queue for progress."
  end

  def down
    # This migration cannot be easily reversed since it involves data transformation
    # The legacy files are not deleted, so the system can still function with legacy files
    say "This migration cannot be automatically reversed."
    say "Legacy file records are preserved and can still be used if needed."
  end
end
