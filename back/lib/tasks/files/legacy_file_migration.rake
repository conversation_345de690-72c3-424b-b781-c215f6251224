# frozen_string_literal: true

namespace :files do
  namespace :legacy_migration do
    desc 'Migrate all legacy files to the new Files::File system'
    task all: :environment do
      puts '[LegacyFileMigration] Starting migration of all legacy files'
      
      service = Files::LegacyFileMigrationService.new
      stats = service.migrate_all
      
      puts '[LegacyFileMigration] Migration completed'
      puts "Containers processed: #{stats[:containers_processed]}"
      puts "Files migrated: #{stats[:files_migrated]}"
      puts "Files skipped: #{stats[:files_skipped]}"
      puts "Errors: #{stats[:errors]}"
      
      if stats[:errors] > 0
        puts "\nContainers with errors:"
        stats[:containers_with_errors].each do |error_info|
          puts "  #{error_info[:container_type]}##{error_info[:container_id]}: #{error_info[:error]}"
        end
        exit 1
      end
    end

    desc 'Migrate legacy files for a specific container type'
    task :container_type, [:type] => :environment do |_task, args|
      container_type = args[:type]
      
      unless container_type
        puts 'Usage: rake files:legacy_migration:container_type[Project]'
        puts 'Available types: Project, Idea, Event, Phase, StaticPage, ProjectFolders::Folder'
        exit 1
      end

      puts "[LegacyFileMigration] Starting migration for container type: #{container_type}"
      
      service = Files::LegacyFileMigrationService.new
      
      # Find the legacy model mapping
      mapping = service.class::LEGACY_FILE_MAPPINGS.find do |_legacy_model, config|
        config[:container_class] == container_type
      end
      
      unless mapping
        puts "Error: No legacy file mapping found for container type: #{container_type}"
        exit 1
      end
      
      legacy_model_name, config = mapping
      service.send(:migrate_legacy_model, legacy_model_name, config)
      
      puts "[LegacyFileMigration] Migration completed for #{container_type}"
    end

    desc 'Migrate legacy files for a specific container instance'
    task :container, [:type, :id] => :environment do |_task, args|
      container_type = args[:type]
      container_id = args[:id]
      
      unless container_type && container_id
        puts 'Usage: rake files:legacy_migration:container[Project,abc-123]'
        exit 1
      end

      puts "[LegacyFileMigration] Starting migration for #{container_type}##{container_id}"
      
      begin
        container_class = container_type.constantize
        container = container_class.find(container_id)
      rescue NameError => e
        puts "Error: Invalid container type: #{container_type} - #{e.message}"
        exit 1
      rescue ActiveRecord::RecordNotFound => e
        puts "Error: Container not found: #{container_type}##{container_id} - #{e.message}"
        exit 1
      end

      service = Files::LegacyFileMigrationService.new
      result = service.migrate_container(container)
      
      if result[:success]
        puts "Successfully migrated #{result[:files_migrated]} files"
        puts "Skipped #{result[:files_skipped]} files"
      else
        puts "Migration failed: #{result[:error]}"
        exit 1
      end
    end

    desc 'Queue background jobs to migrate all legacy files'
    task queue_jobs: :environment do
      puts '[LegacyFileMigration] Queuing background jobs for legacy file migration'
      
      # Use the same logic as the migration
      legacy_file_mappings = {
        'ProjectFile' => {
          container_class: 'Project',
          container_foreign_key: 'project_id',
          legacy_relationship: 'project_files'
        },
        'IdeaFile' => {
          container_class: 'Idea',
          container_foreign_key: 'idea_id',
          legacy_relationship: 'idea_files'
        },
        'EventFile' => {
          container_class: 'Event',
          container_foreign_key: 'event_id',
          legacy_relationship: 'event_files'
        },
        'PhaseFile' => {
          container_class: 'Phase',
          container_foreign_key: 'phase_id',
          legacy_relationship: 'phase_files'
        },
        'StaticPageFile' => {
          container_class: 'StaticPage',
          container_foreign_key: 'static_page_id',
          legacy_relationship: 'static_page_files'
        },
        'ProjectFolders::File' => {
          container_class: 'ProjectFolders::Folder',
          container_foreign_key: 'project_folder_id',
          legacy_relationship: 'files'
        }
      }

      total_jobs_queued = 0

      legacy_file_mappings.each do |legacy_model_name, config|
        container_class = config[:container_class].constantize
        legacy_relationship = config[:legacy_relationship]
        
        # Find all containers that have legacy files
        containers_with_legacy_files = container_class
          .joins(legacy_relationship.to_sym)
          .distinct
          .select(:id)

        containers_count = containers_with_legacy_files.count
        puts "Found #{containers_count} #{config[:container_class]} containers with legacy files"

        # Queue a job for each container
        containers_with_legacy_files.find_each do |container|
          Files::LegacyFileMigrationJob.perform_later(
            container_type: config[:container_class],
            container_id: container.id
          )
          total_jobs_queued += 1
        end
      end

      puts "Queued #{total_jobs_queued} migration jobs"
      puts "Monitor job queue for progress: rails jobs:work"
    end

    desc 'Show statistics about legacy files'
    task stats: :environment do
      puts '[LegacyFileMigration] Legacy file statistics'
      puts '=' * 50
      
      legacy_file_mappings = Files::LegacyFileMigrationService::LEGACY_FILE_MAPPINGS
      
      total_legacy_files = 0
      total_containers_with_files = 0
      total_migrated_containers = 0
      
      legacy_file_mappings.each do |legacy_model_name, config|
        container_class = config[:container_class].constantize
        legacy_relationship = config[:legacy_relationship]
        
        # Count legacy files
        legacy_files_count = legacy_model_name.constantize.count
        
        # Count containers with legacy files
        containers_with_files = container_class
          .joins(legacy_relationship.to_sym)
          .distinct
          .count
        
        # Count containers that already have new file attachments
        migrated_containers = container_class
          .joins(:file_attachments)
          .distinct
          .count
        
        puts "#{legacy_model_name}:"
        puts "  Legacy files: #{legacy_files_count}"
        puts "  Containers with files: #{containers_with_files}"
        puts "  Already migrated: #{migrated_containers}"
        puts "  Pending migration: #{containers_with_files - migrated_containers}"
        puts
        
        total_legacy_files += legacy_files_count
        total_containers_with_files += containers_with_files
        total_migrated_containers += migrated_containers
      end
      
      puts "TOTALS:"
      puts "  Total legacy files: #{total_legacy_files}"
      puts "  Total containers with files: #{total_containers_with_files}"
      puts "  Total migrated containers: #{total_migrated_containers}"
      puts "  Total pending migration: #{total_containers_with_files - total_migrated_containers}"
    end
  end
end
