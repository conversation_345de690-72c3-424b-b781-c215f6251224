# frozen_string_literal: true

module Files
  # Service to migrate legacy file records to the new Files::File system
  #
  # This service handles the migration of legacy file models (IdeaFile, ProjectFile,
  # EventFile, PhaseFile, StaticPageFile, ProjectFolders::File) to the new unified
  # Files::File and Files::FileAttachment system.
  #
  # Key features:
  # - Transactional per container (all files for a container migrate atomically)
  # - Idempotent (safe to run multiple times)
  # - Preserves all file metadata, content, and ordering
  # - Creates the appropriate Files::FilesProject record if applicable
  #
  # Usage:
  #   # Migrate all legacy files
  #   Files::LegacyFileMigrationService.new.migrate_all
  #
  #   # Migrate files for a specific container
  #   Files::LegacyFileMigrationService.new.migrate_container(project)
  #
  class LegacyFileMigrationService
    include ActiveSupport::Benchmarkable

    # Mapping of legacy file models to their container types and relationships
    LEGACY_FILE_MAPPINGS = {
      'ProjectFile' => {
        container_class: 'Project',
        container_foreign_key: 'project_id',
        legacy_relationship: 'project_files'
      },
      'IdeaFile' => {
        container_class: 'Idea',
        container_foreign_key: 'idea_id',
        legacy_relationship: 'idea_files'
      },
      'EventFile' => {
        container_class: 'Event',
        container_foreign_key: 'event_id',
        legacy_relationship: 'event_files'
      },
      'PhaseFile' => {
        container_class: 'Phase',
        container_foreign_key: 'phase_id',
        legacy_relationship: 'phase_files'
      },
      'StaticPageFile' => {
        container_class: 'StaticPage',
        container_foreign_key: 'static_page_id',
        legacy_relationship: 'static_page_files'
      },
      'ProjectFolders::File' => {
        container_class: 'ProjectFolders::Folder',
        container_foreign_key: 'project_folder_id',
        legacy_relationship: 'files'
      }
    }.freeze

    attr_reader :logger, :stats

    def initialize(logger: Rails.logger)
      @logger = logger
      @stats = {
        containers_processed: 0,
        files_migrated: 0,
        files_skipped: 0,
        errors: 0,
        containers_with_errors: []
      }
    end

    # Migrate all legacy files across all container types
    def migrate_all
      logger.info '[LegacyFileMigration] Starting migration of all legacy files'

      benchmark '[LegacyFileMigration] Total migration time' do
        LEGACY_FILE_MAPPINGS.each do |legacy_model_name, config|
          migrate_legacy_model(legacy_model_name, config)
        end
      end

      log_final_stats
      stats
    end

    # Migrate files for a specific container instance
    # @param container [Project, Idea, Event, Phase, StaticPage, ProjectFolders::Folder]
    def migrate_container(container)
      container_type = container.class.name
      legacy_model_name = find_legacy_model_for_container(container_type)

      unless legacy_model_name
        logger.warn "[LegacyFileMigration] No legacy file model found for container type: #{container_type}"
        return { success: false, error: "Unsupported container type: #{container_type}" }
      end

      config = LEGACY_FILE_MAPPINGS[legacy_model_name]

      logger.info "[LegacyFileMigration] Migrating files for #{container_type}##{container.id}"

      result = nil
      benchmark "[LegacyFileMigration] Container #{container_type}##{container.id} migration time" do
        result = migrate_container_files(container, legacy_model_name, config)
      end

      result
    end

    private

    def migrate_legacy_model(legacy_model_name, config)
      logger.info "[LegacyFileMigration] Processing legacy model: #{legacy_model_name}"

      container_class = config[:container_class].constantize
      legacy_relationship = config[:legacy_relationship]

      # Find all containers that have legacy files
      containers_with_legacy_files = container_class
        .joins(legacy_relationship.to_sym)
        .distinct
        .includes(legacy_relationship.to_sym)

      containers_count = containers_with_legacy_files.count
      logger.info "[LegacyFileMigration] Found #{containers_count} #{config[:container_class]} containers with legacy files"

      containers_with_legacy_files.find_each.with_index do |container, index|
        logger.info "[LegacyFileMigration] Processing #{config[:container_class]}##{container.id} (#{index + 1}/#{containers_count})"

        result = migrate_container_files(container, legacy_model_name, config)

        if result[:success]
          @stats[:containers_processed] += 1
          @stats[:files_migrated] += result[:files_migrated]
          @stats[:files_skipped] += result[:files_skipped]
        else
          @stats[:errors] += 1
          @stats[:containers_with_errors] << {
            container_type: config[:container_class],
            container_id: container.id,
            error: result[:error]
          }
        end
      end
    end

    def find_legacy_model_for_container(container_type)
      LEGACY_FILE_MAPPINGS.find do |_legacy_model, config|
        config[:container_class] == container_type
      end&.first
    end

    def log_final_stats
      logger.info '[LegacyFileMigration] Migration completed'
      logger.info "[LegacyFileMigration] Containers processed: #{@stats[:containers_processed]}"
      logger.info "[LegacyFileMigration] Files migrated: #{@stats[:files_migrated]}"
      logger.info "[LegacyFileMigration] Files skipped: #{@stats[:files_skipped]}"
      logger.info "[LegacyFileMigration] Errors: #{@stats[:errors]}"

      if @stats[:containers_with_errors].any?
        logger.error '[LegacyFileMigration] Containers with errors:'
        @stats[:containers_with_errors].each do |error_info|
          logger.error "  #{error_info[:container_type]}##{error_info[:container_id]}: #{error_info[:error]}"
        end
      end
    end

    # Migrate files for a specific container with transaction safety
    def migrate_container_files(container, legacy_model_name, config)
      container_type = container.class.name
      legacy_files = container.send(config[:legacy_relationship]).order(:ordering)

      return { success: true, files_migrated: 0, files_skipped: 0 } if legacy_files.empty?

      # Check if container already has new file attachments (idempotency check)
      if container.file_attachments.exists?
        logger.info "[LegacyFileMigration] #{container_type}##{container.id} already has file attachments, skipping"
        return { success: true, files_migrated: 0, files_skipped: legacy_files.count }
      end

      files_migrated = 0
      files_skipped = 0

      # Wrap the entire container's file migration in a transaction
      ActiveRecord::Base.transaction do
        legacy_files.each_with_index do |legacy_file, index|
          logger.debug "[LegacyFileMigration] Migrating #{legacy_model_name}##{legacy_file.id} (#{index + 1}/#{legacy_files.count})"

          if migrate_legacy_file(legacy_file, container, legacy_model_name)
            files_migrated += 1
          else
            files_skipped += 1
          end
        end
      end

      logger.info "[LegacyFileMigration] Successfully migrated #{files_migrated} files for #{container_type}##{container.id}"
      { success: true, files_migrated: files_migrated, files_skipped: files_skipped }
    rescue StandardError => e
      logger.error "[LegacyFileMigration] Error migrating files for #{container_type}##{container.id}: #{e.message}"
      logger.error e.backtrace.join("\n") if logger.debug?
      { success: false, error: e.message, files_migrated: 0, files_skipped: 0 }
    end

    # Resolve the project for a given container to create Files::FilesProject records
    def resolve_project_for_container(container)
      case container
      when Project
        container
      when Phase, Event
        container.project
      when Idea
        container.project
      when ProjectFolders::Folder
        # Project folders don't have a direct project relationship
        # Files in project folders are not associated with a specific project
        nil
      when StaticPage
        # Static pages are not associated with projects
        nil
      else
        logger.warn "[LegacyFileMigration] Unknown container type for project resolution: #{container.class.name}"
        nil
      end
    end

    # Check if a container type supports the new file attachment system
    def container_supports_file_attachments?(container)
      Files::FileAttachment::ATTACHABLE_TYPES.include?(container.class.name)
    end

    # Migrate a single legacy file to the new system
    def migrate_legacy_file(legacy_file, container, legacy_model_name)
      # Skip if this legacy file has already been migrated (idempotency check)
      if legacy_file_already_migrated?(legacy_file)
        logger.debug "[LegacyFileMigration] #{legacy_model_name}##{legacy_file.id} already migrated, skipping"
        return false
      end

      # Validate that the container supports file attachments
      unless container_supports_file_attachments?(container)
        logger.warn "[LegacyFileMigration] Container #{container.class.name} does not support file attachments"
        return false
      end

      # Create the new Files::File record
      files_file = create_files_file_from_legacy(legacy_file)
      return false unless files_file&.persisted?

      # Create the Files::FileAttachment record
      file_attachment = create_file_attachment(files_file, container, legacy_file)
      return false unless file_attachment&.persisted?

      # Create Files::FilesProject record if applicable
      project = resolve_project_for_container(container)
      if project
        files_project = create_files_project(files_file, project)
        return false unless files_project&.persisted?
      end

      logger.debug "[LegacyFileMigration] Successfully migrated #{legacy_model_name}##{legacy_file.id}"
      true
    rescue StandardError => e
      logger.error "[LegacyFileMigration] Error migrating #{legacy_model_name}##{legacy_file.id}: #{e.message}"
      false
    end

    # Create a Files::File record from a legacy file
    def create_files_file_from_legacy(legacy_file)
      # Extract file metadata
      file_attributes = {
        name: legacy_file.name,
        content: legacy_file.file, # The file content from the legacy uploader
        uploader: get_legacy_file_uploader(legacy_file),
        created_at: legacy_file.created_at,
        updated_at: legacy_file.updated_at,
        category: 'other' # Default category for migrated files
      }

      files_file = Files::File.new(file_attributes)

      if files_file.save
        logger.debug "[LegacyFileMigration] Created Files::File##{files_file.id} from #{legacy_file.class.name}##{legacy_file.id}"
        files_file
      else
        logger.error "[LegacyFileMigration] Failed to create Files::File from #{legacy_file.class.name}##{legacy_file.id}: #{files_file.errors.full_messages.join(', ')}"
        nil
      end
    end

    # Create a Files::FileAttachment record
    def create_file_attachment(files_file, container, legacy_file)
      attachment_attributes = {
        file: files_file,
        attachable: container,
        position: legacy_file.ordering || 1,
        created_at: legacy_file.created_at,
        updated_at: legacy_file.updated_at
      }

      file_attachment = Files::FileAttachment.new(attachment_attributes)

      if file_attachment.save
        logger.debug "[LegacyFileMigration] Created Files::FileAttachment##{file_attachment.id}"
        file_attachment
      else
        logger.error "[LegacyFileMigration] Failed to create Files::FileAttachment: #{file_attachment.errors.full_messages.join(', ')}"
        nil
      end
    end

    # Create a Files::FilesProject record if the container has an associated project
    def create_files_project(files_file, project)
      # Check if this relationship already exists
      existing = Files::FilesProject.find_by(file: files_file, project: project)
      return existing if existing

      files_project_attributes = {
        file: files_file,
        project: project
      }

      files_project = Files::FilesProject.new(files_project_attributes)

      if files_project.save
        logger.debug "[LegacyFileMigration] Created Files::FilesProject##{files_project.id}"
        files_project
      else
        logger.error "[LegacyFileMigration] Failed to create Files::FilesProject: #{files_project.errors.full_messages.join(', ')}"
        nil
      end
    end

    # Check if a legacy file has already been migrated
    # This is a simple heuristic based on file name and container
    def legacy_file_already_migrated?(_legacy_file)
      # We can't easily track which specific legacy file was migrated,
      # but we can check if the container already has file attachments
      # This is handled at the container level in migrate_container_files
      false
    end

    # Get the uploader for a legacy file (for migration purposes)
    def get_legacy_file_uploader(_legacy_file)
      # Most legacy files don't track the uploader, so we'll leave it nil
      # In a real migration, you might want to set this to an admin user
      nil
    end
  end
end
