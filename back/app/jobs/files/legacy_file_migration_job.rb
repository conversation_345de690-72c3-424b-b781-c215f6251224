# frozen_string_literal: true

module Files
  # Background job to migrate legacy file records to the new Files::File system
  #
  # This job can be used to migrate files for specific containers or all containers
  # in the background, allowing for safe migration without blocking the application.
  #
  # Usage:
  #   # Migrate all legacy files
  #   Files::LegacyFileMigrationJob.perform_later
  #
  #   # Migrate files for a specific container
  #   Files::LegacyFileMigrationJob.perform_later(container_type: 'Project', container_id: project.id)
  #
  class LegacyFileMigrationJob < ApplicationJob
    queue_as :default

    # Maximum number of retries for failed migrations
    retry_on StandardError, wait: :exponentially_longer, attempts: 3

    def perform(container_type: nil, container_id: nil)
      service = Files::LegacyFileMigrationService.new(logger: logger)

      if container_type && container_id
        # Migrate files for a specific container
        migrate_specific_container(service, container_type, container_id)
      else
        # Migrate all legacy files
        migrate_all_containers(service)
      end
    end

    private

    def migrate_specific_container(service, container_type, container_id)
      logger.info "[LegacyFileMigrationJob] Starting migration for #{container_type}##{container_id}"

      container = find_container(container_type, container_id)
      unless container
        logger.error "[LegacyFileMigrationJob] Container not found: #{container_type}##{container_id}"
        return
      end

      result = service.migrate_container(container)

      if result[:success]
        logger.info "[LegacyFileMigrationJob] Successfully migrated #{result[:files_migrated]} files for #{container_type}##{container_id}"
        logger.info "[LegacyFileMigrationJob] Skipped #{result[:files_skipped]} files for #{container_type}##{container_id}"
      else
        logger.error "[LegacyFileMigrationJob] Failed to migrate files for #{container_type}##{container_id}: #{result[:error]}"
        raise StandardError, "Migration failed for #{container_type}##{container_id}: #{result[:error]}"
      end
    end

    def migrate_all_containers(service)
      logger.info '[LegacyFileMigrationJob] Starting migration of all legacy files'

      stats = service.migrate_all

      logger.info '[LegacyFileMigrationJob] Migration completed'
      logger.info "[LegacyFileMigrationJob] Final stats: #{stats}"

      if stats[:errors] > 0
        error_message = "Migration completed with #{stats[:errors]} errors. Check logs for details."
        logger.error "[LegacyFileMigrationJob] #{error_message}"
        raise StandardError, error_message
      end
    end

    def find_container(container_type, container_id)
      container_class = container_type.constantize
      container_class.find_by(id: container_id)
    rescue NameError => e
      logger.error "[LegacyFileMigrationJob] Invalid container type: #{container_type} - #{e.message}"
      nil
    rescue ActiveRecord::RecordNotFound => e
      logger.error "[LegacyFileMigrationJob] Container not found: #{container_type}##{container_id} - #{e.message}"
      nil
    end

    def logger
      Rails.logger
    end
  end
end
