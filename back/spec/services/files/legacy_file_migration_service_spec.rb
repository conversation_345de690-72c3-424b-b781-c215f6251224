# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Files::LegacyFileMigrationService do
  subject(:service) { described_class.new(logger: logger) }

  let(:logger) { instance_double(Logger, info: nil, debug: nil, warn: nil, error: nil) }

  describe '#initialize' do
    it 'sets up the service with default logger' do
      service = described_class.new
      expect(service.logger).to eq(Rails.logger)
    end

    it 'accepts a custom logger' do
      custom_logger = Logger.new(StringIO.new)
      service = described_class.new(logger: custom_logger)
      expect(service.logger).to eq(custom_logger)
    end

    it 'initializes stats hash' do
      expect(service.stats).to eq({
        containers_processed: 0,
        files_migrated: 0,
        files_skipped: 0,
        errors: 0,
        containers_with_errors: []
      })
    end
  end

  describe '#migrate_container' do
    let(:project) { create(:project) }

    context 'when container has legacy files' do
      let!(:project_file1) { create(:project_file, project: project, name: 'file1.pdf', ordering: 1) }
      let!(:project_file2) { create(:project_file, project: project, name: 'file2.doc', ordering: 2) }

      it 'migrates all files for the container' do
        result = service.migrate_container(project)

        expect(result[:success]).to be true
        expect(result[:files_migrated]).to eq 2
        expect(result[:files_skipped]).to eq 0

        # Check that Files::File records were created
        expect(Files::File.count).to eq 2
        files = Files::File.order(:created_at)
        expect(files.first.name).to eq 'file1.pdf'
        expect(files.second.name).to eq 'file2.doc'

        # Check that Files::FileAttachment records were created
        expect(Files::FileAttachment.count).to eq 2
        attachments = project.file_attachments.order(:position)
        expect(attachments.first.position).to eq 1
        expect(attachments.second.position).to eq 2

        # Check that Files::FilesProject records were created
        expect(Files::FilesProject.count).to eq 2
        expect(Files::FilesProject.all.map(&:project)).to all(eq(project))
      end

      it 'preserves file metadata' do
        original_created_at = 1.day.ago
        original_updated_at = 1.hour.ago
        project_file1.update_columns(created_at: original_created_at, updated_at: original_updated_at)

        service.migrate_container(project)

        files_file = Files::File.find_by(name: 'file1.pdf')
        expect(files_file.created_at).to be_within(1.second).of(original_created_at)
        expect(files_file.updated_at).to be_within(1.second).of(original_updated_at)

        file_attachment = Files::FileAttachment.find_by(file: files_file)
        expect(file_attachment.created_at).to be_within(1.second).of(original_created_at)
        expect(file_attachment.updated_at).to be_within(1.second).of(original_updated_at)
      end

      it 'is transactional - all files migrate or none do' do
        # Mock a failure during Files::File creation for the second file
        call_count = 0
        allow(Files::File).to receive(:new) do |*args|
          call_count += 1
          if call_count == 2
            raise StandardError, 'Test error'
          else
            Files::File.new(*args)
          end
        end

        result = service.migrate_container(project)

        expect(result[:success]).to be false
        expect(result[:error]).to include('Test error')

        # No files should have been migrated due to transaction rollback
        expect(Files::File.count).to eq 0
        expect(Files::FileAttachment.count).to eq 0
        expect(Files::FilesProject.count).to eq 0
      end
    end

    context 'when container has no legacy files' do
      it 'returns success with zero counts' do
        result = service.migrate_container(project)

        expect(result[:success]).to be true
        expect(result[:files_migrated]).to eq 0
        expect(result[:files_skipped]).to eq 0
      end
    end

    context 'when container already has file attachments (idempotency)' do
      let!(:project_file) { create(:project_file, project: project, name: 'file.pdf') }
      let!(:existing_attachment) { create(:file_attachment, attachable: project) }

      it 'skips migration and returns skipped count' do
        result = service.migrate_container(project)

        expect(result[:success]).to be true
        expect(result[:files_migrated]).to eq 0
        expect(result[:files_skipped]).to eq 1

        # Should not create additional files
        expect(Files::File.count).to eq 1 # Only the existing one
        expect(Files::FileAttachment.count).to eq 1 # Only the existing one
      end
    end

    context 'when container type is unsupported' do
      let(:unsupported_container) { create(:user) }

      it 'returns error for unsupported container type' do
        result = service.migrate_container(unsupported_container)

        expect(result[:success]).to be false
        expect(result[:error]).to include('Unsupported container type: User')
      end
    end
  end

  describe '#resolve_project_for_container' do
    it 'returns the project itself for Project containers' do
      project = create(:project)
      expect(service.send(:resolve_project_for_container, project)).to eq project
    end

    it 'returns the associated project for Phase containers' do
      project = create(:project)
      phase = create(:phase, project: project)
      expect(service.send(:resolve_project_for_container, phase)).to eq project
    end

    it 'returns the associated project for Event containers' do
      project = create(:project)
      event = create(:event, project: project)
      expect(service.send(:resolve_project_for_container, event)).to eq project
    end

    it 'returns the associated project for Idea containers' do
      project = create(:project)
      idea = create(:idea, project: project)
      expect(service.send(:resolve_project_for_container, idea)).to eq project
    end

    it 'returns nil for StaticPage containers' do
      static_page = create(:static_page)
      expect(service.send(:resolve_project_for_container, static_page)).to be_nil
    end

    it 'returns nil for ProjectFolders::Folder containers' do
      folder = create(:project_folder)
      expect(service.send(:resolve_project_for_container, folder)).to be_nil
    end
  end

  describe 'container type support' do
    it 'supports all expected container types' do
      supported_types = %w[Project Phase Event Idea StaticPage]
      supported_types.each do |type|
        container = instance_double(type, class: double(name: type))
        expect(service.send(:container_supports_file_attachments?, container)).to be true
      end
    end

    it 'does not support unsupported container types' do
      unsupported_container = instance_double('User', class: double(name: 'User'))
      expect(service.send(:container_supports_file_attachments?, unsupported_container)).to be false
    end
  end

  describe 'different container types' do
    context 'with Ideas' do
      let(:project) { create(:project) }
      let(:idea) { create(:idea, project: project) }
      let!(:idea_file) { create(:idea_file, idea: idea, name: 'idea_file.pdf', ordering: 1) }

      it 'migrates idea files correctly' do
        result = service.migrate_container(idea)

        expect(result[:success]).to be true
        expect(result[:files_migrated]).to eq 1

        files_file = Files::File.find_by(name: 'idea_file.pdf')
        expect(files_file).to be_present

        attachment = Files::FileAttachment.find_by(attachable: idea)
        expect(attachment.file).to eq files_file
        expect(attachment.position).to eq 1

        # Should create Files::FilesProject for idea's project
        files_project = Files::FilesProject.find_by(file: files_file, project: project)
        expect(files_project).to be_present
      end
    end

    context 'with Events' do
      let(:project) { create(:project) }
      let(:event) { create(:event, project: project) }
      let!(:event_file) { create(:event_file, event: event, name: 'event_file.pdf', ordering: 2) }

      it 'migrates event files correctly' do
        result = service.migrate_container(event)

        expect(result[:success]).to be true
        expect(result[:files_migrated]).to eq 1

        files_file = Files::File.find_by(name: 'event_file.pdf')
        expect(files_file).to be_present

        attachment = Files::FileAttachment.find_by(attachable: event)
        expect(attachment.file).to eq files_file
        expect(attachment.position).to eq 1 # positioned gem starts from 1

        # Should create Files::FilesProject for event's project
        files_project = Files::FilesProject.find_by(file: files_file, project: project)
        expect(files_project).to be_present
      end
    end

    context 'with StaticPages' do
      let(:static_page) { create(:static_page) }
      let!(:static_page_file) { create(:static_page_file, static_page: static_page, name: 'page_file.pdf', ordering: 3) }

      it 'migrates static page files without project association' do
        result = service.migrate_container(static_page)

        expect(result[:success]).to be true
        expect(result[:files_migrated]).to eq 1

        files_file = Files::File.find_by(name: 'page_file.pdf')
        expect(files_file).to be_present

        attachment = Files::FileAttachment.find_by(attachable: static_page)
        expect(attachment.file).to eq files_file
        expect(attachment.position).to eq 1 # positioned gem starts from 1

        # Should NOT create Files::FilesProject for static pages
        files_project = Files::FilesProject.find_by(file: files_file)
        expect(files_project).to be_nil
      end
    end
  end

  describe '#migrate_all' do
    let!(:project) { create(:project) }
    let!(:idea) { create(:idea, project: project) }
    let!(:event) { create(:event, project: project) }

    let!(:project_file) { create(:project_file, project: project, name: 'project.pdf') }
    let!(:idea_file) { create(:idea_file, idea: idea, name: 'idea.pdf') }
    let!(:event_file) { create(:event_file, event: event, name: 'event.pdf') }

    it 'migrates all legacy files across all container types' do
      stats = service.migrate_all

      expect(stats[:containers_processed]).to eq 3
      expect(stats[:files_migrated]).to eq 3
      expect(stats[:files_skipped]).to eq 0
      expect(stats[:errors]).to eq 0

      # Verify all files were created
      expect(Files::File.count).to eq 3
      expect(Files::FileAttachment.count).to eq 3
      expect(Files::FilesProject.count).to eq 3 # All have project associations

      # Verify file names
      file_names = Files::File.pluck(:name)
      expect(file_names).to contain_exactly('project.pdf', 'idea.pdf', 'event.pdf')
    end

    it 'handles errors gracefully and continues processing' do
      # Mock an error for one container
      allow(service).to receive(:migrate_container_files).and_call_original
      allow(service).to receive(:migrate_container_files).with(project, anything, anything)
        .and_return({ success: false, error: 'Test error', files_migrated: 0, files_skipped: 0 })

      stats = service.migrate_all

      expect(stats[:containers_processed]).to eq 2 # Only idea and event succeeded
      expect(stats[:files_migrated]).to eq 2
      expect(stats[:errors]).to eq 1
      expect(stats[:containers_with_errors].size).to eq 1
      expect(stats[:containers_with_errors].first[:error]).to eq 'Test error'
    end

    it 'logs progress and final statistics' do
      expect(logger).to receive(:info).with('[LegacyFileMigration] Starting migration of all legacy files')
      expect(logger).to receive(:info).with('[LegacyFileMigration] Migration completed')
      expect(logger).to receive(:info).with('[LegacyFileMigration] Containers processed: 3')
      expect(logger).to receive(:info).with('[LegacyFileMigration] Files migrated: 3')

      service.migrate_all
    end
  end

  describe 'error handling' do
    let(:project) { create(:project) }
    let!(:project_file) { create(:project_file, project: project, name: 'file.pdf') }

    it 'handles Files::File creation errors' do
      # Mock the Files::File to fail validation
      allow_any_instance_of(Files::File).to receive(:save).and_return(false)
      allow_any_instance_of(Files::File).to receive(:errors).and_return(double(full_messages: ['Name is invalid']))

      result = service.migrate_container(project)

      expect(result[:success]).to be false
      expect(logger).to have_received(:error).with(match(/Failed to create Files::File/))
    end

    it 'handles Files::FileAttachment creation errors' do
      # Mock the Files::FileAttachment to fail validation
      allow_any_instance_of(Files::FileAttachment).to receive(:save).and_return(false)
      allow_any_instance_of(Files::FileAttachment).to receive(:errors).and_return(double(full_messages: ['Position is invalid']))

      result = service.migrate_container(project)

      expect(result[:success]).to be false
      expect(logger).to have_received(:error).with(match(/Failed to create Files::FileAttachment/))
    end

    it 'handles Files::FilesProject creation errors' do
      # Mock the Files::FilesProject to fail validation
      allow_any_instance_of(Files::FilesProject).to receive(:save).and_return(false)
      allow_any_instance_of(Files::FilesProject).to receive(:errors).and_return(double(full_messages: ['Project is invalid']))

      result = service.migrate_container(project)

      expect(result[:success]).to be false
      expect(logger).to have_received(:error).with(match(/Failed to create Files::FilesProject/))
    end
  end

  describe 'legacy file mappings' do
    it 'defines correct mappings for all legacy file types' do
      mappings = described_class::LEGACY_FILE_MAPPINGS

      expect(mappings).to include(
        'ProjectFile' => {
          container_class: 'Project',
          container_foreign_key: 'project_id',
          legacy_relationship: 'project_files'
        },
        'IdeaFile' => {
          container_class: 'Idea',
          container_foreign_key: 'idea_id',
          legacy_relationship: 'idea_files'
        },
        'EventFile' => {
          container_class: 'Event',
          container_foreign_key: 'event_id',
          legacy_relationship: 'event_files'
        },
        'PhaseFile' => {
          container_class: 'Phase',
          container_foreign_key: 'phase_id',
          legacy_relationship: 'phase_files'
        },
        'StaticPageFile' => {
          container_class: 'StaticPage',
          container_foreign_key: 'static_page_id',
          legacy_relationship: 'static_page_files'
        },
        'ProjectFolders::File' => {
          container_class: 'ProjectFolders::Folder',
          container_foreign_key: 'project_folder_id',
          legacy_relationship: 'files'
        }
      )
    end
  end

  describe 'file content preservation' do
    let(:project) { create(:project) }
    let!(:project_file) { create(:project_file, project: project, name: 'test.pdf') }

    it 'preserves file content from legacy uploader' do
      service.migrate_container(project)

      files_file = Files::File.find_by(name: 'test.pdf')
      expect(files_file.content).to be_present # File content should be preserved
    end

    it 'sets default category for migrated files' do
      service.migrate_container(project)

      files_file = Files::File.find_by(name: 'test.pdf')
      expect(files_file.category).to eq 'other'
    end
  end
end
