require 'rails_helper'

describe Files::LegacyFileMigrationService do
  it '...' do
    event = create(:event)
    event_files = create_list(:event_file, 3, event: event)

    Event.where.associated(:event_files).find_each do |event|
      Files::File.transaction do
        event.event_files.find_each do |event_file|
          file = Files::File.new(
            name: event_file.name,
            content: event_file.file,
            uploader: nil,
            created_at: event_file.created_at
          )

          file.attachments.build(
            position: event_file.ordering,
            attachable: event
          )

          file.files_projects.build(project: event.project)

          file.save!

          event_file.update!(migrated: true)
        # https://teams.live.com/l/invite/FEA3CHPoao0JvtIMx0
        rescue StandardError => e
          binding.pry
        end
      end
    end
    require 'pry'
    binding.pry
  end
end
